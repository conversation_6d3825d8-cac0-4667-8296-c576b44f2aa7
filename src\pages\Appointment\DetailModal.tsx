import { Gender } from '@/constant';
import { calculateAge } from '@/utils/calc';
import { Button, Descriptions, Divider, Modal, Space, Tag } from 'antd';
import { DescriptionsItemType } from 'antd/es/descriptions';
import React, { useEffect, useState } from 'react';
import AdditionalServiceModal from './AdditionalServiceModal';

const DetailModal: React.FC<{
  current: API.Order | undefined;
  open: boolean;
  onClose: () => void;
}> = ({ open, current, onClose }) => {
  const [items_order, setItems_order] = useState<DescriptionsItemType[]>([]);
  const [items_service, setItems_service] = useState<DescriptionsItemType[]>(
    [],
  );
  const [items_customer, setItems_customer] = useState<DescriptionsItemType[]>(
    [],
  );
  const [additionalServiceVisible, setAdditionalServiceVisible] = useState(false);
  const [currentOrderDetailId, setCurrentOrderDetailId] = useState<number>();

  useEffect(() => {
    if (!current) {
      setItems_order([]);
      setItems_service([]);
      setItems_customer([]);
      return;
    }
    // 订单信息
    const items_order: DescriptionsItemType[] = [
      { label: '订单号', span: 2, children: current.sn },
      { label: '订单状态', span: 1, children: current.status },
      {
        label: '预约时间',
        span: 2,
        children: current.serviceTime
          ? new Date(current.serviceTime).toLocaleDateString()
          : '待预约',
      },
      { label: '订单金额', span: 'filled', children: '¥' + current.totalFee },
      {
        label: '预约地址',
        span: 'filled',
        children: current.addressDetail,
      },
      {
        label: '最近出入口',
        span: 'filled',
        children: current.addressRemark,
      },
      {
        label: '追加服务',
        span: 2,
        children: current.hasAdditionalServices ? (
          <Space>
            <Tag color="blue">
              有追加服务 (¥{current.additionalServiceAmount})
            </Tag>
            <Tag color={current.additionalServicesCompleted ? 'green' : 'orange'}>
              {current.additionalServicesCompleted ? '已完成' : '进行中'}
            </Tag>
          </Space>
        ) : (
          <Tag color="default">无追加服务</Tag>
        ),
      },
      {
        label: '操作',
        span: 1,
        children: current.hasAdditionalServices ? (
          <Button
            type="link"
            size="small"
            onClick={() => {
              setCurrentOrderDetailId(current.orderDetails?.[0]?.id);
              setAdditionalServiceVisible(true);
            }}
          >
            查看详情
          </Button>
        ) : null,
      },
    ];
    setItems_order(items_order);
    // 服务信息
    const items_service: DescriptionsItemType[] = [];
    for (const detail of current.orderDetails || []) {
      items_service.push({
        label: '服务名称',
        span: 1,
        children: detail.service?.serviceName,
      });
      items_service.push({
        label: '服务类型',
        span: 'filled',
        children: detail.service?.serviceType?.name,
      });
      items_service.push({
        label: '服务增项',
        span: 'filled',
        children: detail.additionalServices
          ?.map((item) => item.name)
          .join('、'),
      });
      items_service.push({
        label: '宠物昵称',
        span: 'filled',
        children: detail.pet?.name,
      });
      items_service.push({
        label: '宠物类型',
        span: 1,
        children: detail.pet?.type === 'cat' ? '猫' : '狗',
      });
      items_service.push({
        label: '宠物品种',
        span: 'filled',
        children: detail.pet?.breed,
      });
      items_service.push({
        label: '宠物性别',
        span: 1,
        children:
          detail.pet?.gender === 0
            ? '未知'
            : detail.pet?.gender === 1
            ? '公'
            : '母',
      });
      items_service.push({
        label: '宠物年龄',
        span: 'filled',
        children: detail.pet?.birthday
          ? calculateAge(detail.pet.birthday) + '岁'
          : '未知',
      });
      items_service.push({
        label: '毛发类型',
        span: 1,
        children: detail.pet?.hairType
          ? detail.pet.hairType === 'short'
            ? '短毛'
            : '长毛'
          : '',
      });
      items_service.push({
        label: '宠物体重',
        span: 'filled',
        children: detail.pet?.weight + 'kg',
      });
    }
    setItems_service(items_service);
    // 宠主信息
    const items_customer: DescriptionsItemType[] = [
      {
        label: '宠主昵称',
        span: 1,
        children: current.customer?.nickname,
      },
      {
        label: '手机号',
        span: 'filled',
        children: current.customer?.phone,
      },
      {
        label: '宠主性别',
        span: 1,
        children: Gender[current.customer?.gender || 0],
      },
      {
        label: '是否是会员',
        span: 'filled',
        children: current.customer?.memberStatus ? '是' : '否',
      },
    ];
    setItems_customer(items_customer);
  }, [current]);

  return (
    <>
      <Modal title="订单详情" open={open} onCancel={onClose} width={800}>
        <Divider>订单详情</Divider>
        <Descriptions
          title="订单信息"
          bordered
          items={items_order}
          style={{ marginBottom: '20px' }}
        />
        <Descriptions
          title="服务信息"
          bordered
          items={items_service}
          style={{ marginBottom: '20px' }}
        />
        <Descriptions
          title="宠主信息"
          bordered
          items={items_customer}
          style={{ marginBottom: '20px' }}
        />
      </Modal>

      <AdditionalServiceModal
        visible={additionalServiceVisible}
        orderDetailId={currentOrderDetailId}
        onClose={() => {
          setAdditionalServiceVisible(false);
          setCurrentOrderDetailId(undefined);
        }}
      />
    </>
  );
};

export default DetailModal;
