import {
  batchFixAbnormalPrice,
  checkAbnormalPrice,
  getAbnormalPriceDetail,
  getAbnormalPriceReport,
  setAbnormalThreshold,
} from '@/services/data-consistency';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  SettingOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Card,
  Col,
  Descriptions,
  Drawer,
  InputNumber,
  message,
  Modal,
  Row,
  Select,
  Space,
  Statistic,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';

const { Text, Title } = Typography;
const { Option } = Select;

interface AbnormalPriceManagerProps {
  onRefreshStats?: () => void;
}

/**
 * 异常价格管理组件
 */
const AbnormalPriceManager: React.FC<AbnormalPriceManagerProps> = ({
  onRefreshStats,
}) => {
  const [loading, setLoading] = useState(false);
  const [threshold, setThreshold] = useState(10);
  const [abnormalOrders, setAbnormalOrders] = useState<
    API.AbnormalPriceOrder[]
  >([]);
  const [abnormalCount, setAbnormalCount] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentOrderDetail, setCurrentOrderDetail] =
    useState<API.AbnormalPriceDetail | null>(null);
  const [reportData, setReportData] = useState<API.AbnormalPriceReport | null>(
    null,
  );
  const [thresholdModalVisible, setThresholdModalVisible] = useState(false);
  const [newThreshold, setNewThreshold] = useState(10);

  // 加载异常价格订单
  const loadAbnormalOrders = async () => {
    setLoading(true);
    try {
      const res = await checkAbnormalPrice({ threshold, limit: 100 });
      if (!res.errCode) {
        setAbnormalOrders(res.data?.abnormalOrders || []);
        setAbnormalCount(res.data?.abnormalCount || 0);
      } else {
        message.error(res.msg || '加载异常价格订单失败');
        setAbnormalOrders([]);
        setAbnormalCount(0);
      }
    } catch (error) {
      message.error('加载异常价格订单失败');
      setAbnormalOrders([]);
      setAbnormalCount(0);
    } finally {
      setLoading(false);
    }
  };

  // 加载统计报告
  const loadReport = async () => {
    try {
      const res = await getAbnormalPriceReport({ threshold });
      if (!res.errCode) {
        setReportData(res.data || null);
      } else {
        console.error('加载统计报告失败:', res.msg);
        setReportData(null);
      }
    } catch (error) {
      console.error('加载统计报告失败:', error);
      setReportData(null);
    }
  };

  // 查看订单详情
  const handleViewDetail = async (orderId: number) => {
    try {
      const res = await getAbnormalPriceDetail(orderId);
      if (!res.errCode) {
        setCurrentOrderDetail(res.data || null);
        setDetailVisible(true);
      } else {
        message.error(res.msg || '获取订单详情失败');
      }
    } catch (error) {
      message.error('获取订单详情失败');
    }
  };

  // 批量修正异常价格
  const handleBatchFix = async (
    fixType: 'recalculate' | 'adjust' = 'recalculate',
  ) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要修正的订单');
      return;
    }

    Modal.confirm({
      title: '确认批量修正',
      content: `确定要${
        fixType === 'recalculate' ? '重新计算' : '调整'
      }选中的 ${selectedRowKeys.length} 个订单的价格吗？`,
      onOk: async () => {
        try {
          const res = await batchFixAbnormalPrice({
            orderIds: selectedRowKeys.join(','),
            fixType,
          });
          if (!res.errCode) {
            message.success(res.data?.message || '修正完成');
            setSelectedRowKeys([]);
            await loadAbnormalOrders();
            onRefreshStats?.();
          } else {
            message.error(res.msg || '修正失败');
          }
        } catch (error) {
          message.error('修正失败');
        }
      },
    });
  };

  // 设置阈值
  const handleSetThreshold = async () => {
    try {
      const res = await setAbnormalThreshold({ threshold: newThreshold });
      if (!res.errCode) {
        message.success(res.data?.message || '阈值设置成功');
        setThreshold(newThreshold);
        setThresholdModalVisible(false);
        await loadAbnormalOrders();
      } else {
        message.error(res.msg || '设置失败');
      }
    } catch (error) {
      message.error('设置失败');
    }
  };

  useEffect(() => {
    loadAbnormalOrders();
    loadReport();
  }, [threshold]);

  const columns: ColumnsType<API.AbnormalPriceOrder> = [
    {
      title: '订单编号',
      dataIndex: 'sn',
      key: 'sn',
      width: 150,
    },
    {
      title: '原价',
      dataIndex: 'originalPrice',
      key: 'originalPrice',
      width: 100,
      render: (value) => `¥${Number(value || 0).toFixed(2)}`,
    },
    {
      title: '实付金额',
      dataIndex: 'totalFee',
      key: 'totalFee',
      width: 100,
      render: (value) => `¥${Number(value || 0).toFixed(2)}`,
    },
    {
      title: '价格差异',
      dataIndex: 'priceDifference',
      key: 'priceDifference',
      width: 100,
      render: (value) => {
        const numValue = Number(value || 0);
        return (
          <Text type={numValue > 0 ? 'danger' : 'success'}>
            {numValue > 0 ? '+' : ''}¥{Number(numValue || 0).toFixed(2)}
          </Text>
        );
      },
    },
    {
      title: '差异率',
      dataIndex: 'differenceRate',
      key: 'differenceRate',
      width: 100,
      render: (value) => {
        const numValue = Number(value || 0);
        return (
          <Badge
            count={`${numValue.toFixed(1)}%`}
            color={numValue > 20 ? 'red' : Number(numValue || 0) > 10 ? 'orange' : 'blue'}
          />
        );
      },
    },
    {
      title: '异常状态',
      dataIndex: 'isConfirmedAbnormal',
      key: 'isConfirmedAbnormal',
      width: 100,
      render: (value) => (
        <Tag
          color={value ? 'red' : 'orange'}
          icon={value ? <WarningOutlined /> : <ExclamationCircleOutlined />}
        >
          {value ? '确认异常' : '疑似异常'}
        </Tag>
      ),
    },
    {
      title: '异常原因',
      dataIndex: 'abnormalReason',
      key: 'abnormalReason',
      ellipsis: true,
      render: (reasons: string[]) => {
        const reasonList = Array.isArray(reasons) ? reasons : [];
        const reasonText = reasonList.join('; ');
        return (
          <Tooltip title={reasonText}>
            <Text ellipsis>{reasonText}</Text>
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetail(record.id)}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <div>
      {/* 统计概览 */}
      {reportData && (
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title="异常订单总数"
                value={reportData.totalAbnormalCount}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title="确认异常"
                value={reportData.confirmedAbnormalCount}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title="疑似异常"
                value={reportData.suspiciousCount}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title="当前阈值"
                value={`${threshold}%`}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 操作栏 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            icon={<CheckCircleOutlined />}
            onClick={loadAbnormalOrders}
            loading={loading}
          >
            检查异常价格
          </Button>
          <Button
            icon={<SettingOutlined />}
            onClick={() => setThresholdModalVisible(true)}
          >
            设置阈值
          </Button>
          <Select
            value={threshold}
            onChange={setThreshold}
            style={{ width: 120 }}
          >
            <Option value={5}>5%</Option>
            <Option value={10}>10%</Option>
            <Option value={15}>15%</Option>
            <Option value={20}>20%</Option>
          </Select>
          <Text type="secondary">异常阈值</Text>
        </Space>
      </Card>

      {/* 批量操作栏 */}
      {selectedRowKeys.length > 0 && (
        <Card size="small" style={{ marginBottom: 16 }}>
          <Space>
            <Text>已选择 {selectedRowKeys.length} 项</Text>
            <Button
              type="primary"
              size="small"
              onClick={() => handleBatchFix('recalculate')}
            >
              重新计算价格
            </Button>
            <Button size="small" onClick={() => handleBatchFix('adjust')}>
              调整价格
            </Button>
            <Button size="small" onClick={() => setSelectedRowKeys([])}>
              取消选择
            </Button>
          </Space>
        </Card>
      )}

      {/* 异常订单列表 */}
      <Card title={`异常价格订单列表 (${abnormalCount})`} size="small">
        <Table
          columns={columns}
          dataSource={abnormalOrders}
          rowKey="id"
          size="small"
          loading={loading}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            getCheckboxProps: (record) => ({
              disabled: !record.isConfirmedAbnormal, // 只有确认异常的订单才能选择
            }),
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 订单详情抽屉 */}
      <Drawer
        title="异常价格订单详情"
        width={800}
        open={detailVisible}
        onClose={() => setDetailVisible(false)}
      >
        {currentOrderDetail && (
          <div>
            <Title level={5}>订单信息</Title>
            <Descriptions
              column={2}
              bordered
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Descriptions.Item label="订单编号">
                {currentOrderDetail.order.sn}
              </Descriptions.Item>
              <Descriptions.Item label="订单状态">
                {currentOrderDetail.order.status}
              </Descriptions.Item>
              <Descriptions.Item label="原价">
                ¥
                {Number(currentOrderDetail.order.originalPrice || 0).toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="实付金额">
                ¥{Number(currentOrderDetail.order.totalFee || 0).toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="权益卡抵扣">
                ¥
                {Number(currentOrderDetail.order.cardDeduction || 0).toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="优惠券抵扣">
                ¥
                {Number(currentOrderDetail.order.couponDeduction || 0).toFixed(
                  2,
                )}
              </Descriptions.Item>
            </Descriptions>

            <Title level={5}>价格分析</Title>
            <Descriptions
              column={2}
              bordered
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Descriptions.Item label="计算价格">
                ¥
                {Number(
                  currentOrderDetail.priceAnalysis.calculatedPrice || 0,
                ).toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="价格差异">
                <Text
                  type={
                    Number(
                      currentOrderDetail.priceAnalysis.priceDifference || 0,
                    ) > 0
                      ? 'danger'
                      : 'success'
                  }
                >
                  ¥
                  {Number(
                    currentOrderDetail.priceAnalysis.priceDifference || 0,
                  ).toFixed(2)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="差异率">
                <Badge
                  count={`${Number(
                    currentOrderDetail.priceAnalysis.differenceRate || 0,
                  ).toFixed(1)}%`}
                  color={
                    Number(
                      currentOrderDetail.priceAnalysis.differenceRate || 0,
                    ) > 20
                      ? 'red'
                      : 'orange'
                  }
                />
              </Descriptions.Item>
              <Descriptions.Item label="异常状态">
                <Tag
                  color={
                    currentOrderDetail.priceAnalysis.isConfirmedAbnormal
                      ? 'red'
                      : 'orange'
                  }
                >
                  {currentOrderDetail.priceAnalysis.isConfirmedAbnormal
                    ? '确认异常'
                    : '疑似异常'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="异常原因" span={2}>
                {Array.isArray(
                  currentOrderDetail.priceAnalysis.abnormalReason,
                ) ? (
                  currentOrderDetail.priceAnalysis.abnormalReason.map(
                    (reason, index) => (
                      <Tag key={index} style={{ marginBottom: 4 }}>
                        {reason}
                      </Tag>
                    ),
                  )
                ) : (
                  <Tag>无异常原因</Tag>
                )}
              </Descriptions.Item>
            </Descriptions>

            <Title level={5}>订单详情</Title>
            <Table
              columns={[
                {
                  title: '服务名称',
                  dataIndex: 'serviceName',
                  key: 'serviceName',
                },
                {
                  title: '订单价格',
                  dataIndex: 'servicePrice',
                  key: 'servicePrice',
                  render: (v) => `¥${Number(v || 0).toFixed(2)}`,
                },
                {
                  title: '当前价格',
                  dataIndex: 'currentServicePrice',
                  key: 'currentServicePrice',
                  render: (v) => `¥${Number(v || 0).toFixed(2)}`,
                },
                {
                  title: '价格差异',
                  dataIndex: 'priceDifference',
                  key: 'priceDifference',
                  render: (v) => `¥${Number(v || 0).toFixed(2)}`,
                },
              ]}
              dataSource={currentOrderDetail.orderDetails}
              rowKey="id"
              size="small"
              pagination={false}
            />
          </div>
        )}
      </Drawer>

      {/* 阈值设置模态框 */}
      <Modal
        title="设置异常价格阈值"
        open={thresholdModalVisible}
        onOk={handleSetThreshold}
        onCancel={() => setThresholdModalVisible(false)}
      >
        <div style={{ marginBottom: 16 }}>
          <Text>当价格差异率超过此阈值时，订单将被标记为异常价格：</Text>
        </div>
        <InputNumber
          value={newThreshold}
          onChange={(value) => setNewThreshold(value || 10)}
          min={1}
          max={100}
          formatter={(value) => `${value}%`}
          parser={(value) => value!.replace('%', '')}
          style={{ width: '100%' }}
        />
        <div style={{ marginTop: 8 }}>
          <Text type="secondary">
            建议设置：5-15%，过低可能产生过多误报，过高可能遗漏真正的异常
          </Text>
        </div>
      </Modal>
    </div>
  );
};

export default AbnormalPriceManager;
